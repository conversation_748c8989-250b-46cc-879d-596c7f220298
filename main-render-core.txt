// src/core/renderer.hpp
#pragma once

#include <memory>
#include <vector>
#include <functional>
#include <atomic>
#include <tbb/parallel_for.h>
#include <tbb/blocked_range2d.h>
#include <embree4/rtcore.h>

#include "math/vec3.hpp"
#include "math/ray.hpp"
#include "scene/scene.hpp"
#include "integrator/integrator.hpp"
#include "sampler/sampler.hpp"
#include "camera/camera.hpp"

namespace photon {

// Forward declarations
class Scene;
class Camera;
class Integrator;
class Sampler;
class Film;

// Render settings structure
struct RenderSettings {
    int width = 1920;
    int height = 1080;
    int samplesPerPixel = 100;
    int maxBounces = 8;
    int tileSize = 64;
    bool useGPU = false;
    bool enableDenoising = true;
    float denoisingStrength = 0.8f;
    
    // Adaptive sampling
    bool adaptiveSampling = true;
    float adaptiveThreshold = 0.01f;
    int adaptiveMinSamples = 16;
    int adaptiveMaxSamples = 1024;
};

// Render statistics
struct RenderStats {
    std::atomic<int> renderedTiles{0};
    std::atomic<int> totalTiles{0};
    std::atomic<int> totalSamples{0};
    std::atomic<float> renderTime{0.0f};
    
    float getProgress() const {
        return totalTiles > 0 ? float(renderedTiles) / float(totalTiles) : 0.0f;
    }
};

// Main renderer class
class Renderer {
public:
    using ProgressCallback = std::function<void(float progress, const RenderStats& stats)>;
    using TileCallback = std::function<void(int x, int y, int width, int height, const float* pixels)>;
    
    Renderer();
    ~Renderer();
    
    // Non-copyable
    Renderer(const Renderer&) = delete;
    Renderer& operator=(const Renderer&) = delete;
    
    // Setup
    void setScene(std::shared_ptr<Scene> scene);
    void setCamera(std::shared_ptr<Camera> camera);
    void setIntegrator(std::shared_ptr<Integrator> integrator);
    void setSettings(const RenderSettings& settings);
    
    // Rendering
    void render();
    void renderTile(int tileX, int tileY);
    void stop();
    bool isRendering() const { return m_isRendering; }
    
    // Callbacks
    void setProgressCallback(ProgressCallback callback) { m_progressCallback = callback; }
    void setTileCallback(TileCallback callback) { m_tileCallback = callback; }
    
    // Results
    const float* getPixels() const { return m_film->getPixels(); }
    const RenderStats& getStats() const { return m_stats; }
    
private:
    // Core components
    std::shared_ptr<Scene> m_scene;
    std::shared_ptr<Camera> m_camera;
    std::shared_ptr<Integrator> m_integrator;
    std::unique_ptr<Film> m_film;
    RenderSettings m_settings;
    
    // Embree device
    RTCDevice m_device;
    
    // State
    std::atomic<bool> m_isRendering{false};
    std::atomic<bool> m_shouldStop{false};
    RenderStats m_stats;
    
    // Callbacks
    ProgressCallback m_progressCallback;
    TileCallback m_tileCallback;
    
    // Internal methods
    void initializeEmbree();
    void buildAccelerationStructure();
    void renderTileInternal(int tileX, int tileY, int tileWidth, int tileHeight);
    Color3 samplePixel(int x, int y, Sampler& sampler);
};

// Film class for image storage
class Film {
public:
    Film(int width, int height) 
        : m_width(width), m_height(height), 
          m_pixels(width * height * 3, 0.0f),
          m_sampleCount(width * height, 0) {}
    
    void addSample(int x, int y, const Color3& color, float weight = 1.0f) {
        if (x < 0 || x >= m_width || y < 0 || y >= m_height) return;
        
        int idx = (y * m_width + x) * 3;
        m_pixels[idx + 0] += color.x * weight;
        m_pixels[idx + 1] += color.y * weight;
        m_pixels[idx + 2] += color.z * weight;
        m_sampleCount[y * m_width + x] += weight;
    }
    
    Color3 getPixel(int x, int y) const {
        int idx = (y * m_width + x) * 3;
        float count = m_sampleCount[y * m_width + x];
        if (count == 0) return Color3(0);
        
        return Color3(
            m_pixels[idx + 0] / count,
            m_pixels[idx + 1] / count,
            m_pixels[idx + 2] / count
        );
    }
    
    const float* getPixels() const { return m_pixels.data(); }
    int getWidth() const { return m_width; }
    int getHeight() const { return m_height; }
    
private:
    int m_width, m_height;
    std::vector<float> m_pixels;
    std::vector<float> m_sampleCount;
};

// Implementation in renderer.cpp
// ... (implementation details)

// Example usage:
/*
int main() {
    // Create renderer
    auto renderer = std::make_unique<Renderer>();
    
    // Setup scene
    auto scene = std::make_shared<Scene>();
    scene->loadFromFile("cornell_box.scene");
    renderer->setScene(scene);
    
    // Setup camera
    auto camera = std::make_shared<PerspectiveCamera>(
        Vec3(0, 1, 3),    // position
        Vec3(0, 1, 0),    // target
        Vec3(0, 1, 0),    // up
        45.0f,            // fov
        16.0f / 9.0f      // aspect
    );
    renderer->setCamera(camera);
    
    // Setup integrator
    auto integrator = std::make_shared<PathTracer>();
    renderer->setIntegrator(integrator);
    
    // Configure settings
    RenderSettings settings;
    settings.width = 1920;
    settings.height = 1080;
    settings.samplesPerPixel = 100;
    renderer->setSettings(settings);
    
    // Set callbacks
    renderer->setProgressCallback([](float progress, const RenderStats& stats) {
        printf("\rRendering: %.1f%% (%d/%d tiles)", 
            progress * 100.0f, 
            stats.renderedTiles.load(), 
            stats.totalTiles.load()
        );
        fflush(stdout);
    });
    
    renderer->setTileCallback([](int x, int y, int w, int h, const float* pixels) {
        // Update UI with tile data
        updateViewport(x, y, w, h, pixels);
    });
    
    // Start rendering
    renderer->render();
    
    // Save result
    saveImage("output.png", renderer->getPixels(), settings.width, settings.height);
    
    return 0;
}
*/

} // namespace photon

// src/core/renderer.cpp
#include "renderer.hpp"
#include <chrono>
#include <iostream>

namespace photon {

Renderer::Renderer() {
    initializeEmbree();
}

Renderer::~Renderer() {
    if (m_device) {
        rtcReleaseDevice(m_device);
    }
}

void Renderer::initializeEmbree() {
    // Initialize Embree
    m_device = rtcNewDevice(nullptr);
    if (!m_device) {
        std::cerr << "Failed to create Embree device!" << std::endl;
        throw std::runtime_error("Embree initialization failed");
    }
    
    // Set error handler
    rtcSetDeviceErrorFunction(m_device, 
        [](void* userPtr, RTCError code, const char* str) {
            std::cerr << "Embree error " << code << ": " << str << std::endl;
        }, nullptr);
}

void Renderer::setScene(std::shared_ptr<Scene> scene) {
    m_scene = scene;
    if (m_scene) {
        buildAccelerationStructure();
    }
}

void Renderer::setCamera(std::shared_ptr<Camera> camera) {
    m_camera = camera;
}

void Renderer::setIntegrator(std::shared_ptr<Integrator> integrator) {
    m_integrator = integrator;
}

void Renderer::setSettings(const RenderSettings& settings) {
    m_settings = settings;
    m_film = std::make_unique<Film>(settings.width, settings.height);
}

void Renderer::buildAccelerationStructure() {
    if (!m_scene) return;
    
    // Build Embree BVH
    RTCScene rtcScene = rtcNewScene(m_device);
    
    // Add geometry to Embree scene
    for (const auto& mesh : m_scene->getMeshes()) {
        RTCGeometry geom = rtcNewGeometry(m_device, RTC_GEOMETRY_TYPE_TRIANGLE);
        
        // Set vertex buffer
        rtcSetSharedGeometryBuffer(geom, RTC_BUFFER_TYPE_VERTEX, 0,
            RTC_FORMAT_FLOAT3, mesh->vertices.data(), 0,
            sizeof(Vec3), mesh->vertices.size());
        
        // Set index buffer
        rtcSetSharedGeometryBuffer(geom, RTC_BUFFER_TYPE_INDEX, 0,
            RTC_FORMAT_UINT3, mesh->indices.data(), 0,
            sizeof(uint3), mesh->indices.size() / 3);
        
        rtcCommitGeometry(geom);
        rtcAttachGeometry(rtcScene, geom);
        rtcReleaseGeometry(geom);
    }
    
    rtcCommitScene(rtcScene);
    m_scene->setEmbreeScene(rtcScene);
}

void Renderer::render() {
    if (!m_scene || !m_camera || !m_integrator || !m_film) {
        throw std::runtime_error("Renderer not properly configured");
    }
    
    m_isRendering = true;
    m_shouldStop = false;
    m_stats = RenderStats{};
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    // Calculate tiles
    int tilesX = (m_settings.width + m_settings.tileSize - 1) / m_settings.tileSize;
    int tilesY = (m_settings.height + m_settings.tileSize - 1) / m_settings.tileSize;
    m_stats.totalTiles = tilesX * tilesY;
    
    // Render tiles in parallel
    tbb::parallel_for(
        tbb::blocked_range2d<int>(0, tilesY, 1, 0, tilesX, 1),
        [this](const tbb::blocked_range2d<int>& range) {
            for (int ty = range.rows().begin(); ty < range.rows().end(); ++ty) {
                for (int tx = range.cols().begin(); tx < range.cols().end(); ++tx) {
                    if (m_shouldStop) return;
                    renderTile(tx, ty);
                }
            }
        }
    );
    
    auto endTime = std::chrono::high_resolution_clock::now();
    m_stats.renderTime = std::chrono::duration<float>(endTime - startTime).count();
    
    m_isRendering = false;
    
    // Final progress callback
    if (m_progressCallback) {
        m_progressCallback(1.0f, m_stats);
    }
}

void Renderer::renderTile(int tileX, int tileY) {
    int x0 = tileX * m_settings.tileSize;
    int y0 = tileY * m_settings.tileSize;
    int x1 = std::min(x0 + m_settings.tileSize, m_settings.width);
    int y1 = std::min(y0 + m_settings.tileSize, m_settings.height);
    
    renderTileInternal(x0, y0, x1 - x0, y1 - y0);
    
    m_stats.renderedTiles++;
    
    // Progress callback
    if (m_progressCallback) {
        m_progressCallback(m_stats.getProgress(), m_stats);
    }
    
    // Tile callback with pixel data
    if (m_tileCallback) {
        std::vector<float> tilePixels((x1 - x0) * (y1 - y0) * 3);
        for (int y = y0; y < y1; ++y) {
            for (int x = x0; x < x1; ++x) {
                Color3 pixel = m_film->getPixel(x, y);
                int idx = ((y - y0) * (x1 - x0) + (x - x0)) * 3;
                tilePixels[idx + 0] = pixel.x;
                tilePixels[idx + 1] = pixel.y;
                tilePixels[idx + 2] = pixel.z;
            }
        }
        m_tileCallback(x0, y0, x1 - x0, y1 - y0, tilePixels.data());
    }
}

void Renderer::renderTileInternal(int x0, int y0, int width, int height) {
    // Create sampler for this tile
    auto sampler = std::make_unique<RandomSampler>(x0 + y0 * m_settings.width);
    
    // Render each pixel in tile
    for (int y = y0; y < y0 + height; ++y) {
        for (int x = x0; x < x0 + width; ++x) {
            // Multiple samples per pixel
            Color3 pixelColor(0);
            for (int s = 0; s < m_settings.samplesPerPixel; ++s) {
                pixelColor += samplePixel(x, y, *sampler);
                m_stats.totalSamples++;
            }
            pixelColor /= float(m_settings.samplesPerPixel);
            
            // Store in film
            m_film->addSample(x, y, pixelColor);
        }
    }
}

Color3 Renderer::samplePixel(int x, int y, Sampler& sampler) {
    // Generate camera ray
    float u = (x + sampler.get1D()) / float(m_settings.width);
    float v = (y + sampler.get1D()) / float(m_settings.height);
    Ray ray = m_camera->generateRay(u, v, sampler);
    
    // Trace ray through scene
    return m_integrator->Li(ray, *m_scene, sampler);
}

void Renderer::stop() {
    m_shouldStop = true;
}

} // namespace photon
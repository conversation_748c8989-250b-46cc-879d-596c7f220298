{"folders": [{"path": ".", "name": "🚀 PhotoRender Engine"}], "settings": {"files.associations": {"*.rb": "ruby", "*.cu": "cuda", "*.cuh": "cuda", "*.hpp": "cpp", "*.cpp": "cpp"}, "editor.formatOnSave": true, "editor.rulers": [80, 120], "editor.minimap.enabled": true, "cmake.configureOnOpen": true, "C_Cpp.default.configurationProvider": "ms-vscode.cmake-tools", "terminal.integrated.defaultProfile.windows": "PowerShell", "git.autofetch": true, "todo-tree.highlights.enabled": true, "better-comments.tags": [{"tag": "!", "color": "#FF2D00", "backgroundColor": "transparent"}, {"tag": "?", "color": "#3498DB", "backgroundColor": "transparent"}, {"tag": "//", "color": "#474747", "strikethrough": true}, {"tag": "todo", "color": "#FF8C00", "backgroundColor": "transparent"}, {"tag": "*", "color": "#98C379", "backgroundColor": "transparent"}]}, "extensions": {"recommendations": ["ms-vscode.cpptools", "ms-vscode.cmake-tools", "twxs.cmake", "rebornix.ruby", "wingrunr21.vscode-ruby", "kriegalex.vscode-cuda", "gruntfuggly.todo-tree", "aaron-bond.better-comments", "streetsidesoftware.code-spell-checker", "vadimcn.vscode-lldb", "ms-vscode.hexeditor", "shd101wyy.markdown-preview-enhanced", "yzhang.markdown-all-in-one", "hediet.vscode-drawio", "humao.rest-client"]}, "launch": {"version": "0.2.0", "configurations": [{"name": "🐛 Debug Renderer Core", "type": "lldb", "request": "launch", "program": "${workspaceFolder}/build/bin/renderer_test", "args": ["--scene", "test/cornell_box.scene"], "cwd": "${workspaceFolder}", "stopOnEntry": false}, {"name": "🔍 Debug SketchUp Plugin", "type": "<PERSON>", "request": "launch", "program": "${workspaceFolder}/src/ruby/main.rb", "useBundler": false}, {"name": "⚡ Profile GPU Kernel", "type": "cuda-gdb", "request": "launch", "program": "${workspaceFolder}/build/bin/gpu_benchmark"}]}, "tasks": {"version": "2.0.0", "tasks": [{"label": "🔨 Build Debug", "type": "cmake", "command": "build", "targets": ["all"], "group": {"kind": "build", "isDefault": true}, "problemMatcher": "$gcc"}, {"label": "🚀 Build Release", "type": "cmake", "command": "build", "targets": ["all"], "preset": "release", "problemMatcher": "$gcc"}, {"label": "🧪 Run Tests", "type": "shell", "command": "ctest", "options": {"cwd": "${workspaceFolder}/build"}, "group": {"kind": "test", "isDefault": true}}, {"label": "📦 Package Extension", "type": "shell", "command": "./scripts/package.sh", "windows": {"command": ".\\scripts\\package.bat"}}]}}